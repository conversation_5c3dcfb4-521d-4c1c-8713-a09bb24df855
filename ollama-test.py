import json
import requests

# Load knowledge base from file
with open("knowledge_base.json", "r") as f:
    knowledge_base = json.load(f)

# Simulated user input
user_symptoms = """
<PERSON>w calved yesterday. She is lying on her side with her neck bent. No urination since calving.
"""

def diagnose(symptoms, kb):
    prompt = [
        {
            "role": "system",
            "content": (
                "You are a veterinary diagnostic assistant. "
                "Use the provided knowledge base to reason about the user's symptoms. "
                "Match symptoms, ask for follow-up questions if uncertain, and suggest a diagnosis and treatment."
            )
        },
        {
            "role": "user",
            "content": (
                f"Knowledge Base:\n{json.dumps(kb, indent=2)}\n\n"
                f"Symptoms reported by user:\n{symptoms.strip()}\n\n"
                f"What is the most likely diagnosis? What follow-up questions should I ask?"
            )
        }
    ]

    response = requests.post(
        "http://localhost:11434/api/chat",
        json={"model": "llama3.2", "messages": prompt, "stream": False},
    )

    if response.status_code == 200:
        result = response.json()
        print("\n=== <PERSON><PERSON><PERSON>'s Diagnosis ===\n")
        print(result["message"]["content"])
    else:
        print("Error communicating with <PERSON><PERSON><PERSON>:", response.text)

diagnose(user_symptoms, knowledge_base)
